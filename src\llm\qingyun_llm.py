"""
QingYun API自定义LLM实现
解决QingYun API响应格式与LangChain不兼容的问题
"""

import os
import json
import requests
from typing import Any, Dict, List, Optional
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, AIMessage, HumanMessage, SystemMessage
from langchain_core.outputs import ChatGeneration, ChatResult
from langchain_core.callbacks.manager import CallbackManagerForLLMRun
from pydantic import Field


class QingYunChatModel(BaseChatModel):
    """QingYun API自定义聊天模型"""
    
    model: str = Field(description="模型名称")
    api_key: str = Field(description="API密钥")
    base_url: str = Field(default="https://api.qingyuntop.top/v1", description="API基础URL")
    temperature: float = Field(default=0.0, description="温度参数")
    seed: Optional[int] = Field(default=42, description="随机种子")
    timeout: int = Field(default=120, description="超时时间")
    
    @property
    def _llm_type(self) -> str:
        """返回LLM类型"""
        return "qingyun"
    
    def _convert_message_to_dict(self, message: BaseMessage) -> Dict[str, Any]:
        """将LangChain消息转换为API格式"""
        if isinstance(message, HumanMessage):
            return {"role": "user", "content": message.content}
        elif isinstance(message, AIMessage):
            return {"role": "assistant", "content": message.content}
        elif isinstance(message, SystemMessage):
            return {"role": "system", "content": message.content}
        else:
            return {"role": "user", "content": str(message.content)}
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """生成聊天完成"""
        
        # 转换消息格式
        api_messages = [self._convert_message_to_dict(msg) for msg in messages]
        
        # 构建请求参数
        payload = {
            "model": self.model,
            "messages": api_messages,
            "temperature": self.temperature,
            "stream": False,  # 明确禁用流式传输
        }
        
        if self.seed is not None:
            payload["seed"] = self.seed
        
        if stop:
            payload["stop"] = stop
        
        # 更新额外参数
        payload.update(kwargs)
        
        # 构建请求头
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            # 发送API请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=self.timeout
            )

            # 打印调试信息
            print(f"QingYun API响应状态码: {response.status_code}")
            print(f"QingYun API响应头: {dict(response.headers)}")
            print(f"QingYun API响应内容: {response.text[:500]}...")

            response.raise_for_status()

            # 尝试解析JSON响应
            try:
                response_data = response.json()
                print(f"JSON解析成功: {response_data}")

                # 检查标准OpenAI格式
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    choice = response_data["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        content = choice["message"]["content"]
                    else:
                        # 备用解析方法
                        content = str(choice.get("text", choice))
                else:
                    # 如果响应格式不标准，尝试直接提取内容
                    content = str(response_data)

            except json.JSONDecodeError:
                # 如果不是JSON，可能是流式响应或其他格式
                content = response.text
                print(f"非JSON响应，使用原始文本: {content[:200]}...")

            # 创建AI消息
            message = AIMessage(content=content)
            generation = ChatGeneration(message=message)

            return ChatResult(generations=[generation])

        except requests.exceptions.RequestException as e:
            raise ValueError(f"QingYun API请求失败: {str(e)}")
        except Exception as e:
            raise ValueError(f"QingYun API调用失败: {str(e)}")
    
    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """异步生成（暂时使用同步实现）"""
        return self._generate(messages, stop, run_manager, **kwargs)


def create_qingyun_model(model_name: str, api_key: str, **kwargs) -> QingYunChatModel:
    """创建QingYun模型实例"""
    return QingYunChatModel(
        model=model_name,
        api_key=api_key,
        **kwargs
    )
