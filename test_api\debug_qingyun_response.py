#!/usr/bin/env python3
"""
调试QingYun API的实际响应格式
直接调用API来查看响应结构
"""

import os
import sys
import json
import requests
from pathlib import Path
from dotenv import load_dotenv

def test_qingyun_api_directly():
    """直接测试QingYun API"""
    # 加载环境变量
    load_dotenv()
    
    api_key = os.getenv("QINGYUN_API_KEY")
    if not api_key:
        print("❌ QINGYUN_API_KEY环境变量未设置")
        return False
    
    print(f"✅ QingYun API密钥已配置")
    
    # API配置
    base_url = "https://api.qingyuntop.top/v1"
    model_name = "meta-llama/llama-4-scout"
    
    # 构建请求
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model_name,
        "messages": [
            {
                "role": "user",
                "content": "请用一句话介绍你自己。"
            }
        ],
        "temperature": 0.0,
        "seed": 42
    }
    
    print(f"\n🔍 直接调用QingYun API")
    print(f"URL: {url}")
    print(f"Model: {model_name}")
    print(f"Headers: {headers}")
    print(f"Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=120)
        
        print(f"\n📊 API响应状态")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                response_json = response.json()
                print(f"\n✅ JSON响应解析成功")
                print(f"响应结构: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
                
                # 检查标准OpenAI格式
                if 'choices' in response_json:
                    print(f"✅ 包含choices字段")
                    if len(response_json['choices']) > 0:
                        choice = response_json['choices'][0]
                        print(f"第一个choice: {json.dumps(choice, indent=2, ensure_ascii=False)}")
                        
                        if 'message' in choice:
                            print(f"✅ 包含message字段")
                            message = choice['message']
                            if 'content' in message:
                                print(f"✅ 包含content字段")
                                print(f"内容: {message['content']}")
                            else:
                                print(f"❌ message中缺少content字段")
                        else:
                            print(f"❌ choice中缺少message字段")
                    else:
                        print(f"❌ choices数组为空")
                else:
                    print(f"❌ 缺少choices字段")
                    print(f"实际字段: {list(response_json.keys())}")
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ API调用失败")
            print(f"错误响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def test_langchain_chatopenai_directly():
    """测试LangChain的ChatOpenAI是否能正确处理QingYun"""
    # 加载环境变量
    load_dotenv()
    
    api_key = os.getenv("QINGYUN_API_KEY")
    if not api_key:
        print("❌ QINGYUN_API_KEY环境变量未设置")
        return False
    
    print(f"\n🔍 测试LangChain ChatOpenAI与QingYun集成")
    
    try:
        from langchain_openai import ChatOpenAI
        from langchain_core.messages import HumanMessage
        
        # 创建ChatOpenAI实例
        llm = ChatOpenAI(
            model="meta-llama/llama-4-scout",
            api_key=api_key,
            base_url="https://api.qingyuntop.top/v1",
            temperature=0.0,
            seed=42,
            timeout=120
        )
        
        print(f"✅ ChatOpenAI实例创建成功")
        
        # 测试调用
        messages = [HumanMessage(content="请用一句话介绍你自己。")]
        
        print(f"🔄 调用LLM...")
        response = llm.invoke(messages)
        
        print(f"✅ LLM调用成功")
        print(f"响应类型: {type(response)}")
        print(f"响应内容: {response}")
        
        if hasattr(response, 'content'):
            print(f"✅ 响应包含content属性")
            print(f"Content: {response.content}")
        else:
            print(f"❌ 响应缺少content属性")
            print(f"响应属性: {dir(response)}")
            
    except Exception as e:
        print(f"❌ LangChain测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 QingYun API响应格式调试")
    print("=" * 50)
    
    # 测试1: 直接API调用
    test_qingyun_api_directly()
    
    # 测试2: LangChain集成
    test_langchain_chatopenai_directly()
    
    print(f"\n📋 调试总结:")
    print(f"1. 检查API响应是否符合OpenAI格式")
    print(f"2. 确认LangChain是否能正确处理响应")
    print(f"3. 识别需要特殊处理的地方")

if __name__ == "__main__":
    main()
