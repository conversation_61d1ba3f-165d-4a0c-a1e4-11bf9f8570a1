#!/usr/bin/env python3
"""
诊断QingYun模型的JSON模式支持
测试不同的调用方式来确定正确的处理方法
"""

import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field
from typing import Literal

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.llm.models import get_model, ModelProvider

class TestSignal(BaseModel):
    """测试用的信号模型"""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(ge=0, le=100, description="Confidence level 0-100")
    reasoning: str = Field(description="Reasoning for the signal")

def test_qingyun_json_modes():
    """测试QingYun模型的不同JSON处理方式"""
    # 加载环境变量
    load_dotenv()
    
    api_key = os.getenv("QINGYUN_API_KEY")
    if not api_key:
        print("❌ QINGYUN_API_KEY环境变量未设置")
        return False
    
    print(f"✅ QingYun API密钥已配置")
    
    model_name = "meta-llama/llama-4-scout"
    
    print(f"\n🧪 测试模型: {model_name}")
    print("=" * 50)
    
    # 测试1: 直接调用（无structured output）
    print(f"\n1️⃣ 测试直接调用（无structured output）")
    try:
        llm = get_model(model_name, ModelProvider.QINGYUN)
        
        prompt = """请分析AAPL股票并返回JSON格式的结果：
{
  "signal": "bullish" or "bearish" or "neutral",
  "confidence": 数字(0-100),
  "reasoning": "分析原因"
}

请只返回JSON，不要其他内容。"""
        
        messages = [HumanMessage(content=prompt)]
        response = llm.invoke(messages)
        
        print(f"✅ 直接调用成功")
        print(f"📝 响应类型: {type(response)}")
        print(f"📝 响应内容: {response.content[:200]}...")
        
        # 尝试解析JSON
        try:
            if hasattr(response, 'content'):
                content = response.content
            else:
                content = str(response)
            
            # 尝试直接解析
            parsed = json.loads(content)
            print(f"✅ JSON解析成功: {parsed}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"原始内容: {content}")
            
    except Exception as e:
        print(f"❌ 直接调用失败: {e}")
    
    # 测试2: 使用structured output
    print(f"\n2️⃣ 测试structured output")
    try:
        llm = get_model(model_name, ModelProvider.QINGYUN)
        structured_llm = llm.with_structured_output(TestSignal, method="json_mode")
        
        prompt = """请分析AAPL股票并返回分析结果。
请返回JSON格式，包含signal（bullish/bearish/neutral）、confidence（0-100）和reasoning字段。"""
        
        messages = [HumanMessage(content=prompt)]
        response = structured_llm.invoke(messages)
        
        print(f"✅ Structured output调用成功")
        print(f"📝 响应类型: {type(response)}")
        print(f"📝 响应内容: {response}")
        
        if isinstance(response, TestSignal):
            print(f"✅ 返回了正确的Pydantic模型")
            print(f"   Signal: {response.signal}")
            print(f"   Confidence: {response.confidence}")
            print(f"   Reasoning: {response.reasoning}")
        else:
            print(f"❌ 返回类型不正确: {type(response)}")
            
    except Exception as e:
        print(f"❌ Structured output调用失败: {e}")
        print(f"错误详情: {str(e)}")
        
        # 检查是否是choices错误
        if "'str' object has no attribute 'choices'" in str(e):
            print(f"🔍 检测到choices错误 - 这表明API响应格式不符合OpenAI标准")
    
    # 测试3: 手动JSON解析
    print(f"\n3️⃣ 测试手动JSON解析")
    try:
        llm = get_model(model_name, ModelProvider.QINGYUN)
        
        prompt = """请分析AAPL股票。请严格按照以下JSON格式返回结果，不要包含任何其他文本：

{
  "signal": "bullish",
  "confidence": 75,
  "reasoning": "分析原因"
}"""
        
        messages = [HumanMessage(content=prompt)]
        response = llm.invoke(messages)
        
        print(f"✅ 手动解析调用成功")
        
        # 提取内容
        if hasattr(response, 'content'):
            content = response.content
        else:
            content = str(response)
        
        print(f"📝 原始响应: {content}")
        
        # 尝试多种JSON提取方法
        from src.utils.llm import extract_json_from_response
        
        parsed_json = extract_json_from_response(content)
        if parsed_json:
            print(f"✅ JSON提取成功: {parsed_json}")
            
            # 尝试创建Pydantic模型
            try:
                signal = TestSignal(**parsed_json)
                print(f"✅ Pydantic模型创建成功: {signal}")
            except Exception as e:
                print(f"❌ Pydantic模型创建失败: {e}")
        else:
            print(f"❌ JSON提取失败")
            
    except Exception as e:
        print(f"❌ 手动解析调用失败: {e}")

def main():
    """主函数"""
    print("🔍 QingYun模型JSON支持诊断")
    print("=" * 50)
    
    try:
        test_qingyun_json_modes()
        
        print(f"\n📋 诊断结论:")
        print(f"1. 如果直接调用成功但structured output失败，说明需要禁用JSON模式")
        print(f"2. 如果出现'choices'错误，说明API响应格式不标准")
        print(f"3. 如果手动解析成功，说明需要使用手动JSON提取")
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")

if __name__ == "__main__":
    main()
