#!/usr/bin/env python3
"""
测试QingYun模型修复后的功能
验证JSON模式禁用和手动解析是否正常工作
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseModel, Field
from typing import Literal

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.llm.models import get_model, ModelProvider, get_model_info
from src.utils.llm import call_llm

class TestSignal(BaseModel):
    """测试用的信号模型"""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(ge=0, le=100, description="Confidence level 0-100")
    reasoning: str = Field(description="Reasoning for the signal")

def test_qingyun_json_mode_disabled():
    """测试QingYun模型是否正确禁用了JSON模式"""
    print("🧪 测试QingYun模型JSON模式配置")
    
    model_name = "meta-llama/llama-4-scout"
    model_provider = "QingYun"
    
    # 检查模型信息
    model_info = get_model_info(model_name, model_provider)
    if model_info:
        print(f"✅ 模型信息获取成功")
        print(f"   模型名称: {model_info.model_name}")
        print(f"   提供商: {model_info.provider}")
        print(f"   支持JSON模式: {model_info.has_json_mode()}")
        
        if not model_info.has_json_mode():
            print(f"✅ QingYun模型正确禁用了JSON模式")
            return True
        else:
            print(f"❌ QingYun模型仍然启用JSON模式")
            return False
    else:
        print(f"❌ 无法获取模型信息")
        return False

def test_qingyun_call_llm():
    """测试通过call_llm函数调用QingYun模型"""
    print(f"\n🧪 测试call_llm函数调用QingYun模型")
    
    # 加载环境变量
    load_dotenv()
    
    api_key = os.getenv("QINGYUN_API_KEY")
    if not api_key:
        print("❌ QINGYUN_API_KEY环境变量未设置")
        return False
    
    model_name = "meta-llama/llama-4-scout"
    model_provider = "QingYun"
    
    # 创建简单的提示
    from langchain_core.prompts import ChatPromptTemplate
    
    template = ChatPromptTemplate.from_messages([
        ("system", "你是一位专业的金融分析师。"),
        ("human", """请分析AAPL股票并返回JSON格式的结果。

请严格按照以下格式返回：
{{
  "signal": "bullish" or "bearish" or "neutral",
  "confidence": 数字(0-100),
  "reasoning": "分析原因"
}}

只返回JSON，不要其他内容。""")
    ])
    
    prompt = template.invoke({})
    
    try:
        print(f"🔄 调用call_llm函数...")
        
        def create_default_signal():
            return TestSignal(
                signal="neutral",
                confidence=0.0,
                reasoning="Error in analysis, defaulting to neutral"
            )
        
        result = call_llm(
            prompt=prompt,
            model_name=model_name,
            model_provider=model_provider,
            pydantic_model=TestSignal,
            agent_name="test_agent",
            default_factory=create_default_signal
        )
        
        print(f"✅ call_llm调用成功")
        print(f"结果类型: {type(result)}")
        print(f"结果内容: {result}")
        
        if isinstance(result, TestSignal):
            print(f"✅ 返回了正确的Pydantic模型")
            print(f"   Signal: {result.signal}")
            print(f"   Confidence: {result.confidence}")
            print(f"   Reasoning: {result.reasoning}")
            return True
        else:
            print(f"❌ 返回类型不正确")
            return False
            
    except Exception as e:
        print(f"❌ call_llm调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_qingyun_direct_model():
    """测试直接调用QingYun模型"""
    print(f"\n🧪 测试直接调用QingYun模型")
    
    # 加载环境变量
    load_dotenv()
    
    api_key = os.getenv("QINGYUN_API_KEY")
    if not api_key:
        print("❌ QINGYUN_API_KEY环境变量未设置")
        return False
    
    try:
        model_name = "meta-llama/llama-4-scout"
        llm = get_model(model_name, ModelProvider.QINGYUN)
        
        print(f"✅ 模型初始化成功")
        print(f"模型类型: {type(llm)}")
        
        # 检查是否禁用了流式传输
        if hasattr(llm, 'streaming'):
            print(f"流式传输设置: {llm.streaming}")
        
        from langchain_core.messages import HumanMessage
        
        messages = [HumanMessage(content="请用一句话介绍你自己。")]
        
        print(f"🔄 调用模型...")
        response = llm.invoke(messages)
        
        print(f"✅ 模型调用成功")
        print(f"响应类型: {type(response)}")
        
        if hasattr(response, 'content'):
            print(f"✅ 响应包含content属性")
            print(f"Content: {response.content}")
            return True
        else:
            print(f"❌ 响应缺少content属性")
            return False
            
    except Exception as e:
        print(f"❌ 直接模型调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 QingYun模型修复验证测试")
    print("=" * 50)
    
    results = []
    
    # 测试1: JSON模式配置
    print(f"\n1️⃣ JSON模式配置测试")
    results.append(test_qingyun_json_mode_disabled())
    
    # 测试2: 直接模型调用
    print(f"\n2️⃣ 直接模型调用测试")
    results.append(test_qingyun_direct_model())
    
    # 测试3: call_llm函数调用
    print(f"\n3️⃣ call_llm函数调用测试")
    results.append(test_qingyun_call_llm())
    
    # 总结
    print(f"\n{'='*50}")
    print(f"🎯 测试结果总结")
    print(f"{'='*50}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"成功: {success_count}/{total_count} 个测试")
    
    if success_count == total_count:
        print(f"🎉 所有测试通过！QingYun模型修复成功。")
        print(f"\n✅ 现在可以在回测中正常使用QingYun模型了：")
        print(f"   python src/backtester.py --tickers NVDA")
        print(f"   然后选择 [qingyun] meta-llama/llama-4-scout")
    elif success_count > 0:
        print(f"⚠️ 部分测试通过，可能还需要进一步调试。")
    else:
        print(f"❌ 所有测试失败，需要进一步排查问题。")

if __name__ == "__main__":
    main()
